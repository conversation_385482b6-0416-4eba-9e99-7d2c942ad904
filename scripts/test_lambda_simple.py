#!/usr/bin/env python3
"""
Script simple para probar la lambda en local - versión básica sin dependencias complejas
"""

import json
import sys
import os
from pathlib import Path

# Añadir el directorio raíz al path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Función principal - prueba básica"""
    print("🧪 PRUEBA SIMPLE DE LAMBDA")
    print("=" * 40)
    
    # Configurar AWS region
    os.environ.setdefault('AWS_DEFAULT_REGION', 'eu-west-1')
    os.environ.setdefault('AWS_REGION', 'eu-west-1')
    
    # Cargar evento
    event_file = project_root / "data" / "api_gateway_post_event_example.json"
    
    if not event_file.exists():
        print(f"❌ No se encontró: {event_file}")
        return
    
    with open(event_file, 'r') as f:
        event = json.load(f)
    
    print(f"✅ Evento cargado")
    
    # Extraer filename
    body = json.loads(event['body'])
    filename = body.get('filename')
    print(f"📁 Filename: {filename}")
    
    # Probar solo el procesamiento de evento
    try:
        from src.lambda_function import process_event
        
        result = process_event(event)
        print(f"✅ process_event funciona:")
        print(f"   - filename: {result['filename']}")
        print(f"   - bucket: {result['bucket']}")
        
    except Exception as e:
        print(f"❌ Error en process_event: {e}")
        return
    
    # Probar handler (puede fallar por dependencias)
    print("\n🚀 Probando handler completo...")
    
    try:
        from src.lambda_function import handler
        
        # Contexto mock simple
        class Context:
            pass
        
        response = handler(event, Context())
        
        print(f"✅ Handler ejecutado:")
        print(f"   - Status: {response['statusCode']}")
        
        if response.get('body'):
            body_parsed = json.loads(response['body'])
            print(f"   - Response: {json.dumps(body_parsed, indent=2, ensure_ascii=False)}")
            
    except Exception as e:
        print(f"⚠️  Handler falló (normal si faltan dependencias): {e}")
        print("💡 Esto puede ser por falta de conexión AWS o dependencias")

if __name__ == "__main__":
    main()
