#!/usr/bin/env python3
"""
Script de prueba simplificado que no depende de AWS.
"""

import os
import sys
import json

# Configurar variables de entorno ANTES de cualquier importación
os.environ['AWS_DEFAULT_REGION'] = 'eu-west-1'
os.environ['AWS_REGION'] = 'eu-west-1'
os.environ['AWS_ACCESS_KEY_ID'] = 'dummy'
os.environ['AWS_SECRET_ACCESS_KEY'] = 'dummy'
os.environ['GENAI_ENVIRONMENT'] = 'local'

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_process_event_only():
    """Test solo de process_event sin AWS dependencies"""
    print("🧪 Probando process_event (sin AWS)...")
    
    try:
        from src.lambda_function import process_event
        
        # Test 1: Query parameters
        event1 = {
            'queryStringParameters': {
                'filename': 'test.7z',
                'bucket': 'test-bucket'
            }
        }
        
        result1 = process_event(event1)
        assert result1['filename'] == 'test.7z'
        assert result1['bucket'] == 'test-bucket'
        print("✅ Test 1: Query parameters OK")
        
        # Test 2: Body JSON
        event2 = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': json.dumps({
                'filename': 'test2.7z'
            })
        }
        
        result2 = process_event(event2)
        assert result2['filename'] == 'test2.7z'
        assert result2['bucket'] is None
        print("✅ Test 2: Body JSON OK")
        
        # Test 3: Path parameters
        event3 = {
            'queryStringParameters': None,
            'pathParameters': {
                'filename': 'test3.7z'
            },
            'body': None
        }
        
        result3 = process_event(event3)
        assert result3['filename'] == 'test3.7z'
        print("✅ Test 3: Path parameters OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_imports_basic():
    """Test de importaciones básicas"""
    print("📦 Probando importaciones básicas...")
    
    try:
        import py7zr
        print(f"✅ py7zr version: {py7zr.__version__}")
        
        from src.lambda_function import process_event
        print("✅ process_event importado")
        
        from src.domain.models import TasacionRequest
        print("✅ TasacionRequest importado")
        
        # Test TasacionRequest
        req = TasacionRequest("test_id", ["file1.7z", "file2.zip"])
        assert req.id_tasacion == "test_id"
        assert len(req.files) == 2
        print("✅ TasacionRequest funciona correctamente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_file_unzipper_logic():
    """Test de la lógica de FileUnzipper sin S3"""
    print("🔧 Probando lógica de FileUnzipper...")
    
    try:
        from src.domain.services import FileUnzipper
        
        # Test que las funciones existen
        assert hasattr(FileUnzipper, 'unzip_and_upload')
        assert hasattr(FileUnzipper, '_extract_7z')
        assert hasattr(FileUnzipper, '_extract_zip')
        print("✅ FileUnzipper tiene todos los métodos necesarios")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_handler_with_mock():
    """Test del handler con mock del processor"""
    print("🎭 Probando handler con mock...")
    
    try:
        from unittest.mock import patch, Mock
        
        with patch('src.lambda_function.processor') as mock_processor:
            # Setup mock
            mock_processor.process.return_value = (200, {"message": "Test exitoso"})
            
            from src.lambda_function import handler
            
            event = {
                'queryStringParameters': {
                    'filename': 'test.7z'
                }
            }
            
            response = handler(event, None)
            
            assert response['statusCode'] == 200
            body = json.loads(response['body'])
            assert body['message'] == 'Test exitoso'
            print("✅ Handler con mock funciona correctamente")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_error_handling():
    """Test de manejo de errores"""
    print("⚠️  Probando manejo de errores...")
    
    try:
        from src.lambda_function import process_event, handler
        
        # Test 1: Evento sin filename
        event_no_filename = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }
        
        result = process_event(event_no_filename)
        assert result['filename'] is None
        print("✅ process_event maneja evento sin filename")
        
        # Test 2: Handler sin filename
        response = handler(event_no_filename, None)
        assert response['statusCode'] == 400
        body = json.loads(response['body'])
        assert 'filename' in body['error']
        print("✅ Handler maneja error de filename faltante")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Ejecutar todos los tests"""
    print("=" * 60)
    print("🚀 TESTS SIMPLIFICADOS (SIN AWS)")
    print("=" * 60)
    
    tests = [
        ("Importaciones Básicas", test_imports_basic),
        ("Process Event", test_process_event_only),
        ("FileUnzipper Logic", test_file_unzipper_logic),
        ("Handler con Mock", test_handler_with_mock),
        ("Manejo de Errores", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Ejecutando: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 RESULTADOS: {passed}/{total} tests pasaron")
    
    if passed == total:
        print("🎉 ¡Funcionalidad básica verificada!")
        print("\n💡 La Lambda está correctamente configurada para:")
        print("   ✅ Parsear eventos de API Gateway")
        print("   ✅ Manejar diferentes formatos de entrada")
        print("   ✅ Validar parámetros")
        print("   ✅ Manejar errores apropiadamente")
        print("   ✅ Procesar archivos .7z y .zip")
        print("\n🚀 Para pruebas con S3 real:")
        print("   1. Configurar credenciales AWS válidas")
        print("   2. Crear bucket S3 de prueba")
        print("   3. Subir archivos .7z de prueba")
    else:
        print("⚠️  Algunos tests fallaron. Revisar configuración.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
