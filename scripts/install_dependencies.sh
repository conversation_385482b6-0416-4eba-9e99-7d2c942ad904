#!/bin/bash

# Script para instalar las dependencias del proyecto

echo "Installing project dependencies..."

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "Poetry is not installed. Please install Poetry first:"
    echo "curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Install dependencies
echo "Installing dependencies with Poetry..."
poetry install

# Check if py7zr was installed correctly
echo "Checking py7zr installation..."
poetry run python -c "import py7zr; print('py7zr version:', py7zr.__version__)"

echo "Dependencies installed successfully!"
echo ""
echo "To run tests:"
echo "  poetry run python scripts/test_local.py"
echo ""
echo "To run unit tests:"
echo "  poetry run pytest tests/test_api_gateway_lambda.py"
echo ""
echo "To activate the virtual environment:"
echo "  poetry shell"
