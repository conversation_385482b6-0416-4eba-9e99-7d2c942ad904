#!/usr/bin/env python3
"""
Script para probar la lambda en local usando el evento de API Gateway
"""

import json
import sys
import os
from pathlib import Path

# Añadir el directorio raíz al path para importar módulos
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def load_api_gateway_event():
    """Carga el evento de API Gateway desde el archivo JSON"""
    event_file = project_root / "data" / "api_gateway_post_event_example.json"
    
    if not event_file.exists():
        raise FileNotFoundError(f"No se encontró el archivo de evento: {event_file}")
    
    with open(event_file, 'r', encoding='utf-8') as f:
        event = json.load(f)
    
    print(f"✅ Evento cargado desde: {event_file}")
    return event

def test_lambda_handler():
    """Prueba la función handler de la lambda"""
    print("🚀 Iniciando prueba de lambda en local...")
    print("=" * 50)
    
    try:
        # Cargar el evento
        event = load_api_gateway_event()
        
        # Mostrar información del evento
        print(f"📋 Información del evento:")
        print(f"   - Método HTTP: {event.get('httpMethod')}")
        print(f"   - Recurso: {event.get('resource')}")
        
        # Extraer filename del body
        if event.get('body'):
            body = json.loads(event['body'])
            filename = body.get('filename')
            print(f"   - Filename: {filename}")
        
        print("\n🔧 Importando lambda handler...")
        
        # Importar la función handler
        from src.lambda_function import handler
        
        print("✅ Handler importado correctamente")
        
        # Crear contexto mock (simple)
        class MockContext:
            def __init__(self):
                self.function_name = "test-lambda"
                self.function_version = "$LATEST"
                self.invoked_function_arn = "arn:aws:lambda:eu-west-1:123456789012:function:test-lambda"
                self.memory_limit_in_mb = 128
                self.remaining_time_in_millis = lambda: 30000
                self.log_group_name = "/aws/lambda/test-lambda"
                self.log_stream_name = "2025/07/10/[$LATEST]test123"
                self.aws_request_id = "test-request-id"
        
        context = MockContext()
        
        print("\n⚡ Ejecutando lambda handler...")
        print("-" * 30)
        
        # Ejecutar la lambda
        response = handler(event, context)
        
        print("\n📤 Respuesta de la lambda:")
        print("-" * 30)
        print(f"Status Code: {response.get('statusCode')}")
        
        # Parsear y mostrar el body de respuesta
        if response.get('body'):
            try:
                body_parsed = json.loads(response['body'])
                print(f"Body: {json.dumps(body_parsed, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError:
                print(f"Body (raw): {response['body']}")
        
        # Determinar si fue exitoso
        status_code = response.get('statusCode', 500)
        if 200 <= status_code < 300:
            print("\n✅ ¡Prueba EXITOSA!")
        else:
            print(f"\n⚠️  Prueba completada con status {status_code}")
            
        return response
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        print("💡 Asegúrate de que todas las dependencias estén instaladas")
        return None
        
    except FileNotFoundError as e:
        print(f"❌ Archivo no encontrado: {e}")
        return None
        
    except Exception as e:
        print(f"❌ Error durante la ejecución: {e}")
        print(f"   Tipo de error: {type(e).__name__}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return None

def test_event_processing():
    """Prueba solo el procesamiento del evento"""
    print("\n🔍 Probando procesamiento de evento...")
    
    try:
        event = load_api_gateway_event()
        
        from src.lambda_function import process_event
        
        result = process_event(event)
        print(f"✅ Evento procesado:")
        print(f"   - Filename: {result.get('filename')}")
        print(f"   - Bucket: {result.get('bucket')}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error procesando evento: {e}")
        return None

def main():
    """Función principal"""
    print("🧪 PRUEBA DE LAMBDA EN LOCAL")
    print("=" * 50)
    
    # Configurar variables de entorno para AWS (local)
    os.environ.setdefault('AWS_DEFAULT_REGION', 'eu-west-1')
    os.environ.setdefault('AWS_REGION', 'eu-west-1')
    
    # Prueba 1: Procesamiento de evento
    test_event_processing()
    
    print("\n" + "=" * 50)
    
    # Prueba 2: Handler completo
    response = test_lambda_handler()
    
    print("\n" + "=" * 50)
    print("🏁 Prueba completada")
    
    return response

if __name__ == "__main__":
    main()
