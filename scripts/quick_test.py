#!/usr/bin/env python3
"""
Script de prueba rápida para verificar que la Lambda funciona correctamente.
"""

import json
import sys
import os
from unittest.mock import Mock, patch

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_basic_functionality():
    """Test básico de funcionalidad"""
    print("🧪 Probando funcionalidad básica...")
    
    try:
        from src.lambda_function import process_event, handler
        
        # Test 1: process_event con query parameters
        event1 = {
            'queryStringParameters': {
                'filename': 'test.7z',
                'bucket': 'test-bucket'
            }
        }
        
        result1 = process_event(event1)
        assert result1['filename'] == 'test.7z'
        assert result1['bucket'] == 'test-bucket'
        print("✅ Test 1 pasado: process_event con query parameters")
        
        # Test 2: process_event con body
        event2 = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': json.dumps({
                'filename': 'test2.7z'
            })
        }
        
        result2 = process_event(event2)
        assert result2['filename'] == 'test2.7z'
        assert result2['bucket'] is None
        print("✅ Test 2 pasado: process_event con body")
        
        # Test 3: handler con filename faltante
        event3 = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }
        
        response3 = handler(event3, None)
        assert response3['statusCode'] == 400
        print("✅ Test 3 pasado: handler maneja filename faltante")
        
        print("🎉 Todos los tests básicos pasaron!")
        return True
        
    except Exception as e:
        print(f"❌ Error en test básico: {e}")
        return False

def test_imports():
    """Test de importaciones"""
    print("📦 Probando importaciones...")
    
    try:
        from src.lambda_function import handler, process_event
        print("✅ src.lambda_function importado correctamente")
        
        from src.application.processor import TasacionProcessor
        print("✅ TasacionProcessor importado correctamente")
        
        from src.domain.services import FileUnzipper, TasacionValidator
        print("✅ Servicios de dominio importados correctamente")
        
        from src.domain.models import TasacionRequest
        print("✅ Modelos de dominio importados correctamente")
        
        import py7zr
        print("✅ py7zr importado correctamente")
        
        print("🎉 Todas las importaciones exitosas!")
        return True
        
    except Exception as e:
        print(f"❌ Error en importaciones: {e}")
        return False

def test_processor_mock():
    """Test del processor con mock"""
    print("🔧 Probando processor con mock...")
    
    try:
        with patch('src.application.processor.TasacionProcessor') as mock_processor_class:
            # Setup mock
            mock_processor = Mock()
            mock_processor.process.return_value = (200, {"message": "Test exitoso"})
            mock_processor_class.return_value = mock_processor
            
            from src.lambda_function import handler
            
            event = {
                'queryStringParameters': {
                    'filename': 'test.7z'
                }
            }
            
            response = handler(event, None)
            
            assert response['statusCode'] == 200
            body = json.loads(response['body'])
            assert body['message'] == 'Test exitoso'
            
            print("✅ Processor mock test pasado")
            return True
            
    except Exception as e:
        print(f"❌ Error en processor mock test: {e}")
        return False

def main():
    """Ejecutar todos los tests"""
    print("=" * 60)
    print("🚀 INICIANDO TESTS RÁPIDOS")
    print("=" * 60)
    
    tests = [
        ("Importaciones", test_imports),
        ("Funcionalidad Básica", test_basic_functionality),
        ("Processor Mock", test_processor_mock)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Ejecutando: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 RESULTADOS: {passed}/{total} tests pasaron")
    
    if passed == total:
        print("🎉 ¡Todos los tests pasaron! La Lambda está lista.")
        print("\n💡 Próximos pasos:")
        print("   1. Instalar dependencias: ./scripts/install_dependencies.sh")
        print("   2. Ejecutar tests completos: poetry run pytest")
        print("   3. Probar localmente: poetry run python scripts/test_local.py")
    else:
        print("⚠️  Algunos tests fallaron. Revisar la configuración.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
