{"resource": "/process-file", "path": "/process-file", "httpMethod": "GET", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Host": "api.example.com", "User-Agent": "curl/7.68.0"}, "multiValueHeaders": {}, "queryStringParameters": {"filename": "company1/provider1/tasacion_data.7z", "bucket": "custom-bucket-name"}, "multiValueQueryStringParameters": {}, "pathParameters": null, "stageVariables": null, "requestContext": {"resourceId": "abc123", "resourcePath": "/process-file", "httpMethod": "GET", "extendedRequestId": "def456", "requestTime": "09/Jul/2025:10:00:00 +0000", "path": "/dev/process-file", "accountId": "************", "protocol": "HTTP/1.1", "stage": "dev", "domainPrefix": "api", "requestTimeEpoch": *************, "requestId": "ghi789", "identity": {"cognitoIdentityPoolId": null, "accountId": null, "cognitoIdentityId": null, "caller": null, "sourceIp": "***********", "principalOrgId": null, "accessKey": null, "cognitoAuthenticationType": null, "cognitoAuthenticationProvider": null, "userArn": null, "userAgent": "curl/7.68.0", "user": null}, "domainName": "api.example.com", "apiId": "jkl012"}, "body": null, "isBase64Encoded": false}