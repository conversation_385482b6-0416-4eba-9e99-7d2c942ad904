import logging
import json
import time
import py7zr
import io
import os

from src.infrastructure.core.config import genai_config
from src.infrastructure.genai.filesystem import S3FileSystemUnitOfWork

env = genai_config.GENAI_ENVIRONMENT
trace_lambda_condition = (env != "local") and (env != "test")
# please do not remove, this preconfigures opentelemetry!!
if trace_lambda_condition: # pragma: no cover
    from genai.libs.observability import setup_observability
    from genai.libs.observability.genai_lambda import trace_lambda

    default_dimensions = { # pragma: no cover
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
    }

    setup_observability(
        environment=genai_config.GENAI_ENVIRONMENT,
        default_dimensions=default_dimensions,
    )

logger = logging.getLogger(__name__)

def get_file_name_and_company(object_key: str):
    """Extract company, provider and folder name from S3 object key.

    Args:
        object_key (str): S3 object key path

    Returns:
        tuple: (company, provider, folder_name)
    """
    # Assuming object_key format: "company/provider/filename.7z"
    parts = object_key.split('/')
    if len(parts) >= 3:
        company = parts[0]
        provider = parts[1]
        folder_name = parts[-1]  # filename
    else:
        # Fallback for different formats
        company = "default"
        provider = "default"
        folder_name = os.path.basename(object_key)

    return company, provider, folder_name

def should_extract_s3(s3_client, object_key: str, extracted_folder_prefix: str, provider: str) -> bool:
    """Check if the file should be extracted based on modification time.

    Args:
        s3_client: S3 client instance
        object_key (str): S3 object key of the compressed file
        extracted_folder_prefix (str): Prefix where extracted files would be stored
        provider (str): Provider name

    Returns:
        bool: True if file should be extracted, False otherwise
    """
    try:
        # Get the last modified time of the compressed file
        compressed_file_info = s3_client._s3_client.head_object(
            Bucket=s3_client._route,
            Key=object_key
        )
        compressed_modified = compressed_file_info['LastModified']

        # Check if extracted folder exists and get its modification time
        folder_path = f"{extracted_folder_prefix}/{provider}/"
        try:
            folder_objects = s3_client.list_folder(folder_path)
            if not folder_objects:
                return True  # No extracted files exist, should extract

            # Get the most recent file in the extracted folder
            latest_extracted = None
            for obj_key in folder_objects:
                obj_info = s3_client._s3_client.head_object(
                    Bucket=s3_client._route,
                    Key=obj_key
                )
                if latest_extracted is None or obj_info['LastModified'] > latest_extracted:
                    latest_extracted = obj_info['LastModified']

            # Extract if compressed file is newer than extracted files
            return compressed_modified > latest_extracted if latest_extracted else True

        except Exception:
            # If we can't check extracted folder, assume we should extract
            return True

    except Exception as e:
        logger.error(f"Error checking if should extract: {e}")
        return True  # Default to extracting if we can't determine

def extract_7z_from_s3(s3_client, object_key: str, extracted_folder_prefix: str,
                      extracted_folder_text_prefix: str, extracted_folder_images_prefix: str,
                      extracted_folder_docs_prefix: str, extracted_folder_register_prefix: str):
    """Extract 7z file from S3 and upload extracted files back to S3.

    Args:
        s3_client: S3 client instance
        object_key (str): S3 object key of the 7z file
        extracted_folder_prefix (str): Base prefix for extracted files
        extracted_folder_text_prefix (str): Prefix for text files
        extracted_folder_images_prefix (str): Prefix for image files
        extracted_folder_docs_prefix (str): Prefix for document files
        extracted_folder_register_prefix (str): Prefix for register files
    """
    try:
        # Download the 7z file from S3
        logger.info(f"Downloading 7z file: {object_key}")
        file_content = s3_client.get_file(object_key)

        # Extract the 7z file
        logger.info(f"Extracting 7z file: {object_key}")
        with py7zr.SevenZipFile(io.BytesIO(file_content), mode='r') as archive:
            for info in archive.list():
                if not info.is_dir:
                    # Read file content
                    extracted_data = archive.read([info.filename])
                    file_content = extracted_data[info.filename].read()

                    # Determine target folder based on file extension
                    file_ext = os.path.splitext(info.filename)[1].lower()

                    if file_ext in ['.txt', '.csv', '.json', '.xml']:
                        target_prefix = extracted_folder_text_prefix
                    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                        target_prefix = extracted_folder_images_prefix
                    elif file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx']:
                        target_prefix = extracted_folder_docs_prefix
                    elif file_ext in ['.log', '.reg']:
                        target_prefix = extracted_folder_register_prefix
                    else:
                        target_prefix = extracted_folder_prefix

                    # Upload extracted file to S3
                    target_key = f"{target_prefix}/{info.filename}"
                    logger.info(f"Uploading extracted file: {target_key}")

                    # Use the S3 client to upload the file
                    s3_client._s3_client.put_object(
                        Bucket=s3_client._route,
                        Key=target_key,
                        Body=file_content
                    )

        logger.info(f"Successfully extracted and uploaded files from: {object_key}")

    except Exception as e:
        logger.error(f"Error extracting 7z file {object_key}: {e}")
        raise e

def conditional_trace_lambda(apply_decorator: bool):
    """
    Returns the trace_lambda function for the decorator if the conditional it's
    True.

    Parameters:
    - apply_decorator (bool): indicates if the decorator trace_lambda has to be
    applied. 
    """
    def decorator(func):
        if apply_decorator:
            return trace_lambda(
                environment=env,
                default_dimensions=default_dimensions
            )(func)
        else:
            return func
    return decorator

def http_response(status_code: int, message: str = None, error: str = None):
    """Create HTTP response for API Gateway.

    Args:
        status_code (int): HTTP status code
        message (str): Success message
        error (str): Error message

    Returns:
        dict: API Gateway response format
    """
    body = {}
    if message:
        body['message'] = message
    if error:
        body['error'] = error

    if not body:
        body['message'] = "Successfully processed" if status_code == 200 else "Error occurred"

    response = {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps(body)
    }
    return response

def process_file_request(filename: str, bucket_name: str = None):
    """Process a file extraction request from API Gateway.

    Args:
        filename (str): Name of the compressed file to process
        bucket_name (str): S3 bucket name (optional, uses default if not provided)

    Returns:
        tuple: (status_code, message, error)
    """
    logger.info("Starting processing of file extraction request...")
    logger.info(f"Requested file: {filename}")

    # Use default bucket if not provided
    if not bucket_name:
        bucket_name = genai_config.S3_BUCKET

    logger.info(f"Using bucket: {bucket_name}")

    # Validate filename
    if not filename:
        return 400, None, "Filename is required"

    # Ensure filename has .7z extension
    if not filename.endswith('.7z'):
        filename = filename + '.7z'

    object_key = filename

    # Initialize S3 client
    call_s3 = S3FileSystemUnitOfWork(bucket_name)

    # Check if file exists in S3
    try:
        call_s3.get_file(object_key)
        logger.info(f"File {object_key} found in S3")
    except Exception as e:
        logger.error(f"File {object_key} not found in S3: {e}")
        return 404, None, f"File {object_key} not found in S3 bucket {bucket_name}"

    # Configuration variables
    output_folder_prefix = genai_config.S3_OUTPUT_PATH
    output_folder_text_prefix = genai_config.S3_OUTPUT_PATH_TEXT
    output_folder_images_prefix = genai_config.S3_OUTPUT_PATH_IMAGES
    output_folder_docs_prefix = genai_config.S3_OUTPUT_PATH_DOCS
    output_folder_register_prefix = genai_config.S3_OUTPUT_PATH_REGISTER

    if object_key.endswith(".7z"):
        logger.info("Processing .7z file...")
        try:
            company, provider, folder_name = get_file_name_and_company(object_key)
            folder_name = folder_name.replace(".7z", "")
            zip_folder_processed = f"{output_folder_prefix}/{company}/{provider}"
            extracted_folder_prefix = f"{output_folder_prefix}/{company}"
            extracted_folder_text_prefix = f"{output_folder_text_prefix}/{company}"
            extracted_folder_images_prefix = f"{output_folder_images_prefix}/{company}"
            extracted_folder_docs_prefix = f"{output_folder_docs_prefix}/{company}"
            extracted_folder_register_prefix = f"{output_folder_register_prefix}/{company}"

            logger.info(f"Processing company: {company}, provider: {provider}")

            if should_extract_s3(call_s3, object_key, extracted_folder_prefix, provider):
                extract_7z_from_s3(call_s3, object_key, extracted_folder_prefix,
                                  extracted_folder_text_prefix, extracted_folder_images_prefix,
                                  extracted_folder_docs_prefix, extracted_folder_register_prefix)
                call_s3.move_file(
                    old_file=object_key,
                    new_file=f"{zip_folder_processed}/{folder_name}.7z"
                )
                return 200, f"Successfully processed and extracted {object_key}", None
            else:
                logger.info(f"File {object_key} already processed, skipping extraction")
                return 200, f"File {object_key} already processed, no extraction needed", None

        except Exception as e:
            logger.error(f"Error processing 7z file {object_key}: {e}")
            return 500, None, f"Error processing file: {str(e)}"

    else:
        return 400, None, f"File {object_key} is not a .7z file"


def parse_api_gateway_event(event):
    """Parse API Gateway event to extract filename and bucket.

    Args:
        event (dict): API Gateway event

    Returns:
        tuple: (filename, bucket_name)
    """
    try:
        # Try to get filename from query parameters
        query_params = event.get('queryStringParameters') or {}
        filename = query_params.get('filename')

        # Try to get filename from path parameters
        if not filename:
            path_params = event.get('pathParameters') or {}
            filename = path_params.get('filename')

        # Try to get filename from request body
        if not filename and event.get('body'):
            try:
                body = json.loads(event['body'])
                filename = body.get('filename')
            except json.JSONDecodeError:
                pass

        # Get bucket name (optional)
        bucket_name = query_params.get('bucket') or None

        return filename, bucket_name

    except Exception as e:
        logger.error(f"Error parsing API Gateway event: {e}")
        return None, None

@conditional_trace_lambda(apply_decorator=trace_lambda_condition)
def handler(event, context):
    t1 = time.perf_counter()
    logger.info("Processing API Gateway event...")
    logger.info(f"Event: {json.dumps(event)}")

    try:
        # Parse the API Gateway event
        filename, bucket_name = parse_api_gateway_event(event)

        if not filename:
            return http_response(400, None, "Missing required parameter: filename")

        # Process the file request
        status_code, message, error = process_file_request(filename, bucket_name)

        t2 = time.perf_counter()
        logger.info(f"Lambda execution took: {(t2 - t1)/60:.2f} minutes")

        return http_response(status_code, message, error)

    except Exception as e:
        logger.error(f"Unexpected error in handler: {e}")
        t2 = time.perf_counter()
        logger.info(f"Lambda execution took: {(t2 - t1)/60:.2f} minutes")
        return http_response(500, None, f"Internal server error: {str(e)}")

if __name__ == "__main__":
    # Test event for API Gateway
    test_event = {
        "queryStringParameters": {
            "filename": "test_company/test_provider/test_file.7z"
        },
        "httpMethod": "GET",
        "path": "/process-file"
    }

    result = handler(test_event, None)
    print(f"Test result: {result}")
