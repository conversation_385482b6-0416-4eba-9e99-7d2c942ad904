import io
import zipfile
import py7zr
import os
import logging
from typing import <PERSON>ple, List
from src.domain.models import TasacionRequest

logger = logging.getLogger(__name__)

class TasacionValidator:
    @staticmethod
    def validate_all_exist(
        request: TasacionRequest, bucket: str, s3_repo
    ) -> Tuple[bool, List[str]]:
        logger.info("🔍 ===== VALIDANDO EXISTENCIA DE ARCHIVOS EN S3 =====")
        logger.info(f"🪣 Bucket: {bucket}")
        logger.info(f"📁 Archivos a validar: {request.files}")

        found = []
        for key in request.files:
            logger.info(f"🔍 Verificando existencia de: {key}")
            exists = s3_repo.exists(bucket, key)
            logger.info(f"   - Resultado: {'✅ EXISTE' if exists else '❌ NO EXISTE'}")

            if exists:
                found.append(key)

        all_exist = len(found) == len(request.files)

        logger.info(f"📋 Resumen de validación:")
        logger.info(f"   - Total archivos: {len(request.files)}")
        logger.info(f"   - Archivos encontrados: {len(found)}")
        logger.info(f"   - Todos existen: {'✅ SÍ' if all_exist else '❌ NO'}")
        logger.info(f"   - Lista encontrados: {found}")

        return all_exist, found

class FileUnzipper:
    @staticmethod
    def unzip_and_upload(key: str, bucket: str, s3_repo) -> None:
        """
        Unzip a compressed file (.zip or .7z) and upload extracted files to S3.

        Args:
            key: S3 key of the compressed file
            bucket: S3 bucket name
            s3_repo: S3 repository instance
        """
        logger.info("📦 ===== INICIANDO DESCOMPRESIÓN =====")
        logger.info(f"📁 Archivo: {key}")
        logger.info(f"🪣 Bucket: {bucket}")

        logger.info("⬇️ Descargando archivo desde S3...")
        raw = s3_repo.get_object(bucket, key)
        logger.info(f"✅ Archivo descargado, tamaño: {len(raw)} bytes")

        if key.lower().endswith('.7z'):
            logger.info("🗜️ Detectado archivo .7z, usando extractor 7z")
            FileUnzipper._extract_7z(raw, key, bucket, s3_repo)
        elif key.lower().endswith('.zip'):
            logger.info("🗜️ Detectado archivo .zip, usando extractor zip")
            FileUnzipper._extract_zip(raw, key, bucket, s3_repo)
        else:
            error_msg = f"Formato de archivo no soportado: {key}"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)

        logger.info("🎉 Descompresión completada exitosamente")

    @staticmethod
    def _extract_zip(raw_data: bytes, key: str, bucket: str, s3_repo) -> None:
        """Extract ZIP file and upload contents to S3."""
        logger.info("📦 Extrayendo archivo ZIP...")

        with zipfile.ZipFile(io.BytesIO(raw_data)) as archive:
            members = archive.namelist()
            logger.info(f"📋 Contenido del ZIP: {len(members)} elementos")

            extracted_count = 0
            for member in members:
                logger.info(f"🔍 Procesando: {member}")

                if not archive.getinfo(member).is_dir():
                    logger.info(f"📄 Extrayendo archivo: {member}")
                    content = archive.read(member)
                    target = f"unzipped/{key.replace('.zip', '')}/{member}"

                    logger.info(f"⬆️ Subiendo a S3: {target}")
                    s3_repo.put_object(bucket, target, content)
                    extracted_count += 1
                    logger.info(f"✅ Archivo subido exitosamente")
                else:
                    logger.info(f"📁 Omitiendo directorio: {member}")

            logger.info(f"🎉 ZIP procesado: {extracted_count} archivos extraídos")

    @staticmethod
    def _extract_7z(raw_data: bytes, key: str, bucket: str, s3_repo) -> None:
        """Extract 7Z file and upload contents to S3."""
        logger.info("🗜️ Extrayendo archivo 7Z...")

        with py7zr.SevenZipFile(io.BytesIO(raw_data), mode='r') as archive:
            file_list = archive.list()
            logger.info(f"📋 Contenido del 7Z: {len(file_list)} elementos")

            extracted_count = 0
            for info in file_list:
                logger.info(f"🔍 Procesando: {info.filename}")

                if not info.is_dir:
                    logger.info(f"📄 Extrayendo archivo: {info.filename}")

                    # Read file content
                    extracted_data = archive.read([info.filename])
                    content = extracted_data[info.filename].read()
                    logger.info(f"✅ Contenido leído: {len(content)} bytes")

                    # Determine target folder based on file extension
                    file_ext = os.path.splitext(info.filename)[1].lower()
                    logger.info(f"🔍 Extensión detectada: {file_ext}")

                    if file_ext in ['.txt', '.csv', '.json', '.xml']:
                        folder_type = 'text'
                    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                        folder_type = 'images'
                    elif file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx']:
                        folder_type = 'docs'
                    elif file_ext in ['.log', '.reg']:
                        folder_type = 'register'
                    else:
                        folder_type = 'general'

                    logger.info(f"📁 Tipo de carpeta asignada: {folder_type}")

                    # Create target path
                    base_name = key.replace('.7z', '')
                    target = f"unzipped/{folder_type}/{base_name}/{info.filename}"

                    logger.info(f"⬆️ Subiendo a S3: {target}")
                    s3_repo.put_object(bucket, target, content)
                    extracted_count += 1
                    logger.info(f"✅ Archivo subido exitosamente")
                else:
                    logger.info(f"📁 Omitiendo directorio: {info.filename}")

            logger.info(f"🎉 7Z procesado: {extracted_count} archivos extraídos y organizados por tipo")