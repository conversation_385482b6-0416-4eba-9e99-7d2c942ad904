import io
import zipfile
import py7zr
import os
from typing import <PERSON>ple, List
from src.domain.models import TasacionRequest

class TasacionValidator:
    @staticmethod
    def validate_all_exist(
        request: TasacionRequest, bucket: str, s3_repo
    ) -> Tuple[bool, List[str]]:
        found = [key for key in request.files if s3_repo.exists(bucket, key)]
        return len(found) == len(request.files), found

class FileUnzipper:
    @staticmethod
    def unzip_and_upload(key: str, bucket: str, s3_repo) -> None:
        """
        Unzip a compressed file (.zip or .7z) and upload extracted files to S3.

        Args:
            key: S3 key of the compressed file
            bucket: S3 bucket name
            s3_repo: S3 repository instance
        """
        raw = s3_repo.get_object(bucket, key)

        if key.lower().endswith('.7z'):
            FileUnzipper._extract_7z(raw, key, bucket, s3_repo)
        elif key.lower().endswith('.zip'):
            FileUnzipper._extract_zip(raw, key, bucket, s3_repo)
        else:
            raise ValueError(f"Unsupported file format: {key}")

    @staticmethod
    def _extract_zip(raw_data: bytes, key: str, bucket: str, s3_repo) -> None:
        """Extract ZIP file and upload contents to S3."""
        with zipfile.ZipFile(io.BytesIO(raw_data)) as archive:
            for member in archive.namelist():
                if not archive.getinfo(member).is_dir():
                    content = archive.read(member)
                    target = f"unzipped/{key.replace('.zip', '')}/{member}"
                    s3_repo.put_object(bucket, target, content)

    @staticmethod
    def _extract_7z(raw_data: bytes, key: str, bucket: str, s3_repo) -> None:
        """Extract 7Z file and upload contents to S3."""
        with py7zr.SevenZipFile(io.BytesIO(raw_data), mode='r') as archive:
            for info in archive.list():
                if not info.is_dir:
                    # Read file content
                    extracted_data = archive.read([info.filename])
                    content = extracted_data[info.filename].read()

                    # Determine target folder based on file extension
                    file_ext = os.path.splitext(info.filename)[1].lower()

                    if file_ext in ['.txt', '.csv', '.json', '.xml']:
                        folder_type = 'text'
                    elif file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                        folder_type = 'images'
                    elif file_ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx']:
                        folder_type = 'docs'
                    elif file_ext in ['.log', '.reg']:
                        folder_type = 'register'
                    else:
                        folder_type = 'general'

                    # Create target path
                    base_name = key.replace('.7z', '')
                    target = f"unzipped/{folder_type}/{base_name}/{info.filename}"
                    s3_repo.put_object(bucket, target, content)