from __future__ import annotations

import abc
from typing import Any

from src.infrastructure.observability import trace_execution


class AbstractLlmUnitOfWork(abc.ABC):
    _model: Any

    def __enter__(self) -> AbstractLlmUnitOfWork:
        return self

    def __exit__(self, *args):
        pass

    @abc.abstractmethod
    def get(self):
        raise NotImplementedError

    @abc.abstractmethod
    @trace_execution
    def execute_prompt(
        self, prompt, model=None, temperature=0.5, max_tokens=4000
    ):
        raise NotImplementedError
