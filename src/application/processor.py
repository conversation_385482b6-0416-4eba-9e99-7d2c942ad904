import logging
from typing import Tu<PERSON>, List, Dict
from src.domain.models import TasacionRequest
from src.domain.services import TasacionValidator, FileUnzipper
from src.infrastructure.s3_repository import S3Repository
from src.infrastructure.sqs_repository import SQSRepository
from src.infrastructure.core.config import genai_config

logger = logging.getLogger(__name__)

class TasacionProcessor:
    def __init__(self):
        logger.info("🏗️ Inicializando TasacionProcessor...")

        self.s3_bucket = genai_config.S3_BUCKET
        logger.info(f"🪣 S3 Bucket configurado: {self.s3_bucket}")

        self.s3_repo = S3Repository()
        logger.info("✅ S3Repository inicializado")

        self.queue_url = getattr(genai_config, 'SQS_URL', None)
        logger.info(f"📬 SQS Queue URL: {self.queue_url}")

        self.sqs_repo = SQSRepository(queue_url=self.queue_url)
        logger.info("✅ SQSRepository inicializado")

        logger.info("🎉 TasacionProcessor inicializado correctamente")

    def process(self, payload: Dict) -> Tuple[int, Dict]:
        logger.info("🚀 ===== INICIO DE PROCESAMIENTO =====")
        logger.info(f"📦 Payload recibido: {payload}")

        try:
            # Crear request de tasación
            logger.info("🏗️ Creando TasacionRequest...")
            req = TasacionRequest(payload['id_tasacion'], payload['files'])
            logger.info(f"✅ TasacionRequest creado:")
            logger.info(f"   - ID: {req.id_tasacion}")
            logger.info(f"   - Files: {req.files}")

            # Use custom bucket if provided, otherwise use default
            bucket_to_use = payload.get('bucket', self.s3_bucket)
            logger.info(f"🪣 Bucket a usar: {bucket_to_use}")

            if bucket_to_use != self.s3_bucket:
                logger.info(f"🔄 Usando bucket personalizado (default: {self.s3_bucket})")

            # Validar que todos los archivos existen en S3
            logger.info("🔍 Validando existencia de archivos en S3...")
            all_ok, found = TasacionValidator.validate_all_exist(
                req, bucket_to_use, self.s3_repo
            )

            logger.info(f"📋 Resultado de validación:")
            logger.info(f"   - Todos encontrados: {all_ok}")
            logger.info(f"   - Archivos encontrados: {found}")

            if not all_ok:
                missing = [f for f in req.files if f not in found]
                logger.error(f"❌ Archivos no encontrados en S3: {missing}")
                logger.error(f"🔍 Archivos buscados: {req.files}")
                logger.error(f"✅ Archivos encontrados: {found}")
                return 404, {"error": f"Archivos no encontrados en S3: {missing}"}

            logger.info("✅ Todos los archivos existen en S3")

            # Procesar archivos comprimidos
            processed_files = []
            logger.info(f"📁 Procesando {len(found)} archivos encontrados...")

            for i, key in enumerate(found, 1):
                logger.info(f"🔄 Procesando archivo {i}/{len(found)}: {key}")

                if key.lower().endswith(('.zip', '.7z')):
                    logger.info(f"📦 Archivo comprimido detectado: {key}")
                    try:
                        logger.info(f"⚡ Iniciando descompresión de: {key}")
                        FileUnzipper.unzip_and_upload(key, bucket_to_use, self.s3_repo)
                        processed_files.append(key)
                        logger.info(f"✅ Archivo procesado exitosamente: {key}")
                    except Exception as e:
                        logger.error(f"💥 Error procesando archivo {key}: {e}")
                        logger.error(f"🔍 Tipo de error: {type(e).__name__}")
                        import traceback
                        logger.error(f"📍 Traceback: {traceback.format_exc()}")
                        return 500, {"error": f"Error procesando archivo {key}: {str(e)}"}
                else:
                    logger.warning(f"⚠️ Archivo {key} no es comprimido (.zip/.7z), omitiendo")

            logger.info(f"🎉 Procesamiento de archivos completado:")
            logger.info(f"   - Archivos procesados: {len(processed_files)}")
            logger.info(f"   - Lista: {processed_files}")

            # Send message to SQS if queue is configured
            if self.queue_url:
                logger.info(f"📬 Enviando mensaje a SQS: {self.queue_url}")
                try:
                    sqs_message = {"id_tasacion": req.id_tasacion, "files": found}
                    logger.info(f"📨 Mensaje SQS: {sqs_message}")

                    self.sqs_repo.send_message(sqs_message)
                    logger.info("✅ Mensaje enviado a SQS exitosamente")
                except Exception as e:
                    logger.warning(f"⚠️ No se pudo enviar mensaje a SQS: {e}")
                    logger.warning(f"🔍 Tipo de error SQS: {type(e).__name__}")
                    # No retornamos error aquí, solo advertencia
            else:
                logger.info("📬 SQS no configurado, omitiendo envío de mensaje")

            # Determinar respuesta final
            if processed_files:
                final_response = {
                    "message": f"Archivos procesados exitosamente: {processed_files}",
                    "processed_files": processed_files,
                    "bucket": bucket_to_use,
                    "total_files_found": len(found),
                    "total_files_processed": len(processed_files),
                    "id_tasacion": req.id_tasacion
                }

                logger.info("🏁 ===== PROCESAMIENTO COMPLETADO EXITOSAMENTE =====")
                logger.info(f"📤 Respuesta final: {final_response}")

                return 200, final_response
            else:
                error_response = {"error": "No se encontraron archivos comprimidos para procesar"}
                logger.warning("⚠️ No se procesaron archivos comprimidos")
                logger.warning(f"📤 Respuesta de advertencia: {error_response}")
                return 400, error_response

        except Exception as e:
            logger.error("💥 ===== ERROR CRÍTICO EN PROCESAMIENTO =====")
            logger.error(f"❌ Error: {e}")
            logger.error(f"🔍 Tipo: {type(e).__name__}")
            import traceback
            logger.error(f"📍 Traceback completo: {traceback.format_exc()}")

            error_response = {"error": f"Error crítico en procesamiento: {str(e)}"}
            logger.error(f"📤 Respuesta de error: {error_response}")

            return 500, error_response
