import logging
from typing import <PERSON><PERSON>, List, Dict
from src.domain.models import TasacionRequest
from src.domain.services import <PERSON>sacionValidator, FileUnzipper
from src.infrastructure.s3_repository import S3Repository
from src.infrastructure.sqs_repository import SQSRepository
from src.infrastructure.core.config import genai_config

logger = logging.getLogger(__name__)

class TasacionProcessor:
    def __init__(self):
        self.s3_bucket = genai_config.S3_BUCKET
        self.s3_repo = S3Repository()
        self.queue_url = getattr(genai_config, 'SQS_URL', None)
        self.sqs_repo = SQSRepository(queue_url=self.queue_url)

    def process(self, payload: Dict) -> Tuple[int, Dict]:
        req = TasacionRequest(payload['id_tasacion'], payload['files'])

        # Use custom bucket if provided, otherwise use default
        bucket_to_use = payload.get('bucket', self.s3_bucket)

        all_ok, found = TasacionValidator.validate_all_exist(
            req, bucket_to_use, self.s3_repo
        )
        if not all_ok:
            missing = [f for f in req.files if f not in found]
            return 404, {"error": f"Archivos no encontrados en S3: {missing}"}

        processed_files = []
        for key in found:
            if key.lower().endswith(('.zip', '.7z')):
                try:
                    FileUnzipper.unzip_and_upload(key, bucket_to_use, self.s3_repo)
                    processed_files.append(key)
                    logger.info(f"Successfully processed file: {key}")
                except Exception as e:
                    logger.error(f"Error processing file {key}: {e}")
                    return 500, {"error": f"Error procesando archivo {key}: {str(e)}"}
            else:
                logger.warning(f"File {key} is not a compressed file, skipping")

        # Send message to SQS if queue is configured
        if self.queue_url:
            try:
                self.sqs_repo.send_message({"id_tasacion": req.id_tasacion, "files": found})
            except Exception as e:
                logger.warning(f"Could not send message to SQS: {e}")

        if processed_files:
            return 200, {
                "message": f"Archivos procesados exitosamente: {processed_files}",
                "processed_files": processed_files,
                "bucket": bucket_to_use
            }
        else:
            return 400, {"error": "No se encontraron archivos comprimidos para procesar"}
