import logging
import os

import boto3
from botocore.exceptions import Client<PERSON>rror
from pydantic import ConfigDict
from pydantic_settings import BaseSettings

logger = logging.getLogger(__name__)


class Config(BaseSettings):

    COMPONENT_NAME: str = "tasaciones_lambda_orq"

    COMPONENT_DESCRIPTION: str = "Lambda handling api gateway calls"

    COMPONENT_VERSION: str = "0.1.0"

    GENAI_ENVIRONMENT: str = "local"

    PROMPT_REPOSITORY_ROUTE: str = "./resources/templates/"

    S3_BUCKET: str = "genai-data-tasaciones-es-work-eu-west-1"

    # S3 Output paths configuration
    S3_OUTPUT_PATH: str = "processed"
    S3_OUTPUT_PATH_TEXT: str = "processed/text"
    S3_OUTPUT_PATH_IMAGES: str = "processed/images"
    S3_OUTPUT_PATH_DOCS: str = "processed/docs"
    S3_OUTPUT_PATH_REGISTER: str = "processed/register"

    GENAI_NAME: str = "tasaciones-rag-tasaciones_lambda_orq"

    GENAI_APPLICATION: str = "tasaciones"

    GENAI_PRODUCT: str = "tasaciones"

    PARAMETER_STORE_CONFIG: str = ""

    SQS_URL: str = "tasaciones-queue-preprocessing"
    # framework configurations

    # integrations configurations

    model_config = ConfigDict(extra="ignore")

    def model_post_init(self, __context):
        parameter_store_path = self.dict().get("PARAMETER_STORE_CONFIG", "")
        parameters = {}
        if (
            parameter_store_path is not None
            and parameter_store_path.strip() != ""
        ):
            parameters = self._get_parameters_from_parameter_store(
                parameter_store_path
            )
        for attribute, value in self.dict().items():
            if attribute in parameters:
                attribute_type = type(getattr(self, attribute))
                value = parameters[attribute]
                setattr(self, attribute, attribute_type(value))
                logger.info(
                    f"se ha cambiado el atributo {attribute} al valor {value}"
                )
            if isinstance(value, str) and value.startswith(
                "arn:aws:secretsmanager"
            ):
                setattr(
                    self, attribute, self._get_value_from_secret_manager(value)
                )

    @staticmethod
    def _get_parameters_from_parameter_store(parameter_store_config):
        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(service_name="ssm")
        parameters_result = {}
        try:
            get_parameters = client.get_parameter(
                Name=parameter_store_config, WithDecryption=False
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e
        parameters = get_parameters["Parameter"]["Value"]
        lines = parameters.splitlines()
        for line in lines:
            # Ignore empty lines
            if line.strip():
                # split line caused by =
                key, value = line.split("=", 1)
                parameters_result[key] = value
        return parameters_result

    @staticmethod
    def _get_value_from_secret_manager(secret_arn):
        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(service_name="secretsmanager")
        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=secret_arn
            )
        except ClientError as e:
            # For a list of exceptions thrown, see
            # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
            raise e
        secret = get_secret_value_response["SecretString"]
        return secret


genai_config = Config(
    _env_file=[
        "./.env",
        os.path.expandvars("./config/${GENAI_ENVIRONMENT}/config.cfg"),
    ]
)
