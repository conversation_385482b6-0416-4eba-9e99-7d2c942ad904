import functools
import logging
import time

import boto3


log = logging.getLogger(__name__)

# OpenAI native client automatic instrumentatio


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


class CustomMetric(metaclass=Singleton):
    def __init__(self, namespace):
        log.debug("Custom metrics: Using AWS CloudWatch")
        self.cloudwatch = boto3.client("cloudwatch")
        self.namespace = namespace

    def put_metric(self, metric_name, metric_value, metric_unit, **kwargs):
        metric_data_list = []
        dimensions = []

        if kwargs:
            for key, value in kwargs["dimensions"].items():
                dimensions.append({"Name": key, "Value": value})

        metric_dict = {
            "MetricName": metric_name,
            "Dimensions": dimensions,
            "Unit": metric_unit,
            "Value": metric_value,
        }
        metric_data_list.append(metric_dict)

        self.cloudwatch.put_metric_data(
            Namespace=self.namespace, MetricData=metric_data_list
        )


def trace_execution(func):

    @functools.wraps(func)
    def wrapper(*args, **kwargs):

        log.info(f"Entering {func.__name__}")
        start_time = time.time()

        result = func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time

        log.info(
            f"Exiting  {func.__name__} "
            f"with result: {result} "
            f"and elapsed time: {elapsed_time:.4f} seconds"
        )
        return result

    return wrapper
