import boto3
from botocore.exceptions import ClientError

class S3Repository:
    def __init__(self, client=None):
        self.client = client or boto3.client('s3')

    def exists(self, bucket: str, key: str) -> bool:
        try:
            self.client.head_object(Bucket=bucket, Key=key)
            return True
        except ClientError:
            return False

    def get_object(self, bucket: str, key: str) -> bytes:
        obj = self.client.get_object(Bucket=bucket, Key=key)
        return obj['Body'].read()

    def put_object(self, bucket: str, key: str, content: bytes) -> None:
        self.client.put_object(Bucket=bucket, Key=key, Body=content)