import json
import logging

from genai.libs.observability import setup_observability
from genai.libs.observability.genai_lambda import trace_lambda
#from src.aws.s3_manager import S3Manager

from src.infrastructure.core.config import genai_config

# Configure AWS region for local testing
import os
if not os.environ.get('AWS_DEFAULT_REGION'):
    os.environ['AWS_DEFAULT_REGION'] = 'eu-west-1'
if not os.environ.get('AWS_REGION'):
    os.environ['AWS_REGION'] = 'eu-west-1'

# please do not remove, this preconfigures opentelemetry!!
default_dimensions = {
    "Lambda": genai_config.GENAI_NAME,
    "Application": genai_config.GENAI_APPLICATION,
    "Product": genai_config.GENAI_PRODUCT,
    "service.name": genai_config.GENAI_NAME,
}

setup_observability(
    environment=genai_config.GENAI_ENVIRONMENT,
    default_dimensions=default_dimensions,
)

logger = logging.getLogger(__name__)

# Initialize processor with error handling for local environment
processor = None
try:
    from src.application.processor import TasacionProcessor
    processor = TasacionProcessor()
    logger.info("TasacionProcessor initialized successfully")
except Exception as e:
    logger.warning(f"Could not initialize TasacionProcessor: {e}")
    logger.info("Running in limited mode - some features may not work")

# Read and Process API Gateway event
def process_event(event):
    """
    Process API Gateway event to extract filename and bucket information.

    Args:
        event: API Gateway event

    Returns:
        dict: Processed event with filename and bucket
    """
    logger.info("🔍 Iniciando procesamiento de evento API Gateway")
    logger.info(f"📋 Estructura del evento recibido: {json.dumps(event, indent=2, default=str)}")

    # Try to get filename from query parameters
    query_params = event.get('queryStringParameters') or {}
    filename = query_params.get('filename')
    logger.info(f"🔍 Buscando filename en query parameters: {query_params}")
    if filename:
        logger.info(f"✅ Filename encontrado en query parameters: {filename}")

    # Try to get filename from path parameters
    if not filename:
        path_params = event.get('pathParameters') or {}
        filename = path_params.get('filename')
        logger.info(f"🔍 Buscando filename en path parameters: {path_params}")
        if filename:
            logger.info(f"✅ Filename encontrado en path parameters: {filename}")

    # Try to get filename from request body
    if not filename and event.get('body'):
        logger.info(f"🔍 Buscando filename en request body: {event.get('body')}")
        try:
            body = json.loads(event['body'])
            filename = body.get('filename')
            logger.info(f"📦 Body parseado correctamente: {body}")
            if filename:
                logger.info(f"✅ Filename encontrado en body: {filename}")
        except json.JSONDecodeError as e:
            logger.warning(f"⚠️ Error parseando JSON del body: {e}")

    # Get bucket name (optional)
    bucket_name = query_params.get('bucket') or None
    logger.info(f"🪣 Buscando bucket en query parameters: {bucket_name}")

    if not bucket_name and event.get('body'):
        try:
            body = json.loads(event['body'])
            bucket_name = body.get('bucket')
            logger.info(f"🪣 Bucket encontrado en body: {bucket_name}")
        except json.JSONDecodeError:
            logger.warning("⚠️ No se pudo parsear body para obtener bucket")

    result = {
        'filename': filename,
        'bucket': bucket_name
    }

    logger.info(f"✅ Evento procesado exitosamente: {result}")
    return result


def http_ok_mssg(mssg: str = None) -> dict:
    """
    Creates an HTTP Json message with status 200 and a mssg

    Parameters:
    -mssg (str)=None: message that wants to be included in the HTTP mssg body.
    If it's None it will put a default message.

    Returns:
    - dict: the HTTP message.
    """
    if mssg is None:
        mssg = "Orchestrator is working correctly."

    response = {
        'statusCode': 200,
        'body': json.dumps(mssg)
    }
    return response

def http_response(status: int, body: dict) -> dict:
    return {'statusCode': status, 'body': json.dumps(body)}

#@trace_lambda  # Commented for local testing
def handler(event, context):
    logger.info("🚀 ===== INICIO DE LAMBDA HANDLER =====")
    logger.info(f"📋 Context info: function_name={getattr(context, 'function_name', 'N/A')}, "
                f"remaining_time={getattr(context, 'get_remaining_time_in_millis', lambda: 'N/A')()}")
    logger.info(f"📨 Evento recibido (raw): {json.dumps(event, indent=2, default=str)}")

    try:
        # Check if processor is available
        logger.info("🔧 Verificando disponibilidad del processor...")
        if not processor:
            logger.error("❌ Processor no está disponible - no se pudo inicializar")
            return http_response(500, {"error": "Service not available - processor not initialized"})

        logger.info("✅ Processor disponible y listo")

        # Process API Gateway event
        logger.info("📝 Procesando evento de API Gateway...")
        payload = process_event(event)
        filename = payload.get('filename')
        bucket = payload.get('bucket')

        logger.info(f"📁 Filename extraído: {filename}")
        logger.info(f"🪣 Bucket extraído: {bucket}")

        # Validate required parameters
        if not filename:
            logger.error("❌ Falta parámetro requerido: filename")
            return http_response(400, {"error": "Missing required parameter: filename"})

        logger.info(f"✅ Filename válido recibido: {filename}")

        # Ensure filename has .7z extension for 7z files or appropriate extension
        original_filename = filename
        if not filename.endswith(('.7z', '.zip')):
            # Default to .7z if no extension provided
            filename = filename + '.7z'
            logger.info(f"🔧 Extensión añadida automáticamente: {original_filename} -> {filename}")
        else:
            logger.info(f"✅ Filename ya tiene extensión válida: {filename}")

        # Use default bucket if not provided
        if not bucket:
            bucket = genai_config.S3_BUCKET
            logger.info(f"🪣 Usando bucket por defecto: {bucket}")
        else:
            logger.info(f"🪣 Usando bucket personalizado: {bucket}")

        # Create a modified payload for the processor
        # The processor expects id_tasacion and files list
        id_tasacion = f"api_request_{filename.replace('/', '_').replace('.', '_')}"
        modified_payload = {
            'id_tasacion': id_tasacion,
            'files': [filename],
            'bucket': bucket
        }

        logger.info(f"📦 Payload creado para processor:")
        logger.info(f"   - id_tasacion: {id_tasacion}")
        logger.info(f"   - files: {modified_payload['files']}")
        logger.info(f"   - bucket: {bucket}")

        # Process the file
        logger.info("⚡ Iniciando procesamiento del archivo...")
        status, resp = processor.process(modified_payload)

        logger.info(f"📤 Processor completado con status: {status}")
        logger.info(f"📋 Respuesta del processor: {resp}")

        final_response = http_response(status, resp)
        logger.info(f"🏁 Respuesta final de lambda: {final_response}")

        return final_response

    except Exception as e:
        logger.error(f"💥 Error crítico procesando evento API Gateway: {e}")
        logger.error(f"🔍 Tipo de error: {type(e).__name__}")
        import traceback
        logger.error(f"📍 Traceback completo: {traceback.format_exc()}")

        error_response = http_response(500, {"error": f"Internal server error: {str(e)}"})
        logger.error(f"❌ Respuesta de error: {error_response}")
        return error_response

"""
@trace_lambda
def handler(event, context):
    logger.info("Processing event...")
    s3_path = genai_config.S3_BUCKET + "/path/to/s3"
    # Procesar evento
    event = process_event(event)
    logger.info("Event processed successfully.")

    # Listar s3
    s3_manager = S3Manager(logger)

    # Unzip
    # Write back to S3 with unzipped files.

    # Send to SQS N paths messages with N files to be preocessed.

    return http_ok_mssg()
"""

