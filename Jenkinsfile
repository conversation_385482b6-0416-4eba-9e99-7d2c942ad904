#!groovy

// Project vars
TRACE="TRACE"
jobName = env.JOB_NAME
jobNameParts = jobName.split('/')
BITBUCKET_PROJECT = jobNameParts[0]

@Library('genai@v1.10.0') _
@Library('sonar') l2

def node_label = "Genai-${BITBUCKET_PROJECT}-${UUID.randomUUID().toString()}"
def podtemplate = """
apiVersion: v1
kind: Pod
metadata:
  annotations:
    com.cloudbees.sidecar-injector/inject: no
spec:
  securityContext:
     runAsUser: 0
  imagePullSecrets:
  - name: registrypullsecret
  containers:
  - name: sonar-scanner
    image: artifactory.globaldevtools.bbva.com:443/hub.docker.com-remote/sonarsource/sonar-scanner-cli:11.1.1.1661_6.2.1
    securityContext:
      runAsUser: 0
    command:
    - cat
    tty: true
    resources:
      limits:
        memory: 512Mi
        cpu: 500m
      requests:
        memory: 256Mi
        cpu: 250m
  - name: python-311
    image: artifactory.globaldevtools.bbva.com:443/hub.docker.com-remote/library/python:3.11
    securityContext:
      runAsUser: 0
    command:
    - cat
    tty: true
    resources:
      limits:
        memory: 1Gi
        cpu: 1
      requests:
        memory: 1Gi
        cpu: 1
  - name: aws-cli
    image: artifactory.globaldevtools.bbva.com:443/hub.docker.com-remote/amazon/aws-cli:2.13.2
    securityContext:
      runAsUser: 0
    command:
    - cat
    tty: true
    resources:
      limits:
        memory: 512Mi
        cpu: 250m
      requests:
        memory: 256Mi
        cpu: 250m
  - name: kaniko
    image: artifactory.globaldevtools.bbva.com:443/gcr.io-remote/kaniko-project/executor:v1.23.2-debug
    imagePullPolicy: IfNotPresent
    securityContext:
      runAsUser: 0
    command:
    - /busybox/cat
    tty: true
    volumeMounts:
    - name: ca-bundles
      mountPath: /kaniko/ssl/certs/ca-certificates.crt
      subPath: ca-certificates.crt
    resources:
      limits:
        memory: "2Gi"
        cpu: 2
      requests:
        memory: "2Gi"
        cpu: 2
  volumes:
  - name: ca-bundles
    configMap:
      defaultMode: 420
      name: ca-bundles
"""

pipeline {
    agent {
        kubernetes {
            label node_label
            defaultContainer 'kaniko'
            yaml podtemplate
        }
    }
    environment {
        JENKINS_CREDENTIAL_ID_BOT_ARTIFACTORY = "BotArtifactory"
        ARTIFACTORY_REPOSITORY = "gl-ai-docker-local"
        ARTIFACTORY_ENDPOINT = 'artifactory.globaldevtools.bbva.com:443'
        AWS_JENKINS_ROLE_NAME = 'CodersC3NoRestrictedRoleCrossJenkinsAccount'
        USER_PROXYNG_DEPLOYMENTS_LIVE = 'ProxyNGDeploymentsLive'
        USER_PROXYNG_DEPLOYMENTS_WORK = 'ProxyNGDeploymentsWork'
        EC2_AGENT_EU_LABEL_LIVE = 'jenkinsagentseu-global-live'
        EC2_AGENT_EU_LABEL_WORK = 'jenkinsagentseu-global-work'
        SONAR_INSTANCE_NAME = 'sonar-datacenter-pro'
    }
    stages {
        stage('Get config file'){
            steps{
                script {
                    configFileProvider(
                        [configFile(fileId: 'ProductConfig', targetLocation: 'product_config.yml')]) {
                    }
                }
            }
        }
        stage('Read Config YML Files') {
            steps {
                script {
                    def product_config = readYaml file: 'product_config.yml'
                    env.AWS_REGION = product_config.AWS.AWS_REGION
                    env.AWS_ACCOUNT_ID_DEV = product_config.AWS.AWS_ACCOUNT_ID_DEV
                    env.AWS_ACCOUNT_ID_WORK = product_config.AWS.AWS_ACCOUNT_ID_WORK
                    env.AWS_ACCOUNT_ID_LIVE = product_config.AWS.AWS_ACCOUNT_ID_LIVE
                    env.PRODUCT = product_config.METADATA.PRODUCT.replaceAll("-", "").replaceAll("_", "")
                    def component_config = readYaml file: 'component_config.yml'
                    env.APPLICATION = component_config.APPLICATION.replaceAll("-", "").replaceAll("_", "")
                    env.SERVICE = component_config.SERVICE
                    env.NAME = component_config.NAME.replaceAll("-", "").replaceAll("_", "")
                    env.TYPE = component_config.TYPE
                    env.COMPONENT = "${TYPE}-${NAME}"
                    env.AWS_SERVICE_NAME = "${APPLICATION}-${COMPONENT}"
                    def regionParts = env.AWS_REGION.toLowerCase().split('-')
                    env.AWS_REGION_PREFIX = regionParts[0]
                    env.IMAGE_REPOSITORY_NAME = "genai/${PRODUCT.toLowerCase()}/${APPLICATION.toLowerCase()}/${COMPONENT.toLowerCase()}"
                }
            }
        }
        stage('Selection') {
            steps {
                script {
                    def GitPushCause = currentBuild.getBuildCauses('jenkins.branch.BranchEventCause')
                    def UserCause = currentBuild.getBuildCauses('hudson.model.Cause$UserIdCause')
                    if (GitPushCause) {
                        env.JOB_RUN = 'Build & Push Docker Image'
                    } else if (UserCause) {
                        switch ("${SERVICE.replaceAll("\\s+", "").toLowerCase()}") {
                            case "apprunner":
                                DEPLOY = "Deploy App Runner Service"
                                break
                            case "lambda":
                                DEPLOY = "Deploy AWS Lambda"
                                break
                        }
                        timeout(time:120, unit:'SECONDS') {
                            env.JOB_RUN = input(message: 'Select the option to execute:', parameters: [choice(name: 'JOB_RUN', choices: ["${DEPLOY}", 'Build & Push Docker Image'], description: 'Select the job to run')])
                        }
                    }
                }
            }
        }
        stage('Start') {
            steps {
                script {
                    try {
                        echo "Start flow on branch ${env.BRANCH_NAME}"
                        checkIfPR() // Check if the build is a PR
                        setEnvironmentVariables() // Set the environment variables
                        getVersion() // Get the version of the Docker image
                        setECRConf(IMAGE_REPOSITORY_NAME, version) // Set the ECR configuration
                    } catch (exception) {
                        echo "An exception occurred"
                        print exception
                        throw exception
                    } finally {
                        echo 'We have reached the end of the pipeline.'
                    }
                }
            }
        }
        stage("AWS Assume Role") {
            steps {
                script {
                    // Assume the Jenkins role in the target account
                    AWS_ASSUME_ROLE = "arn:aws:iam::${env.AWS_ACCOUNT_ID}:role/${AWS_JENKINS_ROLE_NAME}"
                    JENKINS_CREDENTIAL_ID_AWS_ROLE_EXTERNAL_ID = 'AWSRoleExternalId' + env.ENVIRONMENT.capitalize()
                    switch (AWS_REGION_PREFIX) {
                        case 'us':
                            withProxyCredentials {
                                awsAssumeRole(AWS_ASSUME_ROLE, "Jenkins", JENKINS_CREDENTIAL_ID_AWS_ROLE_EXTERNAL_ID)
                            }
                            break
                        case 'eu':
                            awsAssumeRole(AWS_ASSUME_ROLE, "Jenkins", JENKINS_CREDENTIAL_ID_AWS_ROLE_EXTERNAL_ID)
                            break
                        default:
                            error("Unsupported region prefix: ${AWS_REGION_PREFIX}")
                    }
                }
            }
        }
        stage("Code Test") {
            when {
                expression {
                    return env.JOB_RUN == 'Build & Push Docker Image'
                }
            }
            steps {
                script {
                    withEnvArtifactoryCredentials {
                        codeTest()
                    }
                }
            }
        }
        stage('Send to Sonar') {
            when {
                expression {
                    return env.JOB_RUN == 'Build & Push Docker Image'
                }
            }
            steps {
                script {
                    sendToSonar()
                }
            }
        }
        stage('Build & Push Docker Image') {
            when {
                expression {
                    return env.JOB_RUN == 'Build & Push Docker Image'
                }
            }
            steps {
                script {
                    withProxyCredentials {
                        if (env.ENVIRONMENT == 'live') {
                            ARTIFACTORY_DESTINATION = "${ARTIFACTORY_ENDPOINT}/${ARTIFACTORY_REPOSITORY}/${IMAGE_REPOSITORY_NAME}:${version}"
                            buildAndPushDockerEcrArtifactory(JENKINS_CREDENTIAL_ID_BOT_ARTIFACTORY, "${env.WORKSPACE}/", AWS_ECR_REGISTRY, AWS_ECR_DESTINATION, ARTIFACTORY_DESTINATION)
                        } else {
                            buildAndPushDockerEcrArtifactory(JENKINS_CREDENTIAL_ID_BOT_ARTIFACTORY, "${env.WORKSPACE}/", AWS_ECR_REGISTRY, AWS_ECR_DESTINATION)
                        }
                    }
                }
            }
        }
        stage ('Deploy service with EC2 agent'){
            when {
                beforeAgent true
                expression { env.JOB_RUN != 'Build & Push Docker Image' && env.AWS_REGION_PREFIX == 'us' }
            }
            agent { label EC2_AGENT_LABEL }
            steps {
                script {
                    withProxyCredentials {
                        deployService()
                    }
                }
            }
        }
        stage ('Deploy service without EC2 agent'){
            when {
                expression { env.JOB_RUN != 'Build & Push Docker Image' && env.AWS_REGION_PREFIX == 'eu' }
            }
            steps {
                script {
                    deployService()
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
        changed {
            echo "There have been some changes from the last build"
        }
        success {
            echo "Build successful"
        }
        failure {
            echo "There have been some errors"
        }
        unstable {
            echo "Unstable"
        }
        aborted {
            echo "Aborted"
        }
    }
}

def setEnvironmentVariables() {
    // Set the environment variables based on the branch name
    switch (env.CODE_BRANCH_NAME) {
        case ~/^release\/.+$/:
        case ~/master$/:
            env.ENVIRONMENT = 'live'
            env.AWS_ACCOUNT_ID = "${AWS_ACCOUNT_ID_LIVE}"
            env.USER_PROXYNG_DEPLOYMENTS = USER_PROXYNG_DEPLOYMENTS_LIVE
            env.EC2_AGENT_LABEL = EC2_AGENT_EU_LABEL_LIVE
            break
        case ~/develop$/:
            env.ENVIRONMENT = 'work'
            env.AWS_ACCOUNT_ID = "${AWS_ACCOUNT_ID_WORK}"
            env.USER_PROXYNG_DEPLOYMENTS = USER_PROXYNG_DEPLOYMENTS_WORK
            env.EC2_AGENT_LABEL = EC2_AGENT_EU_LABEL_WORK
            break
        case ~/^hotfix\/.+$/:
        case ~/^feature\/.+$/:
            env.ENVIRONMENT = 'dev'
            env.AWS_ACCOUNT_ID = "${AWS_ACCOUNT_ID_DEV}"
            env.USER_PROXYNG_DEPLOYMENTS = USER_PROXYNG_DEPLOYMENTS_WORK
            env.EC2_AGENT_LABEL = EC2_AGENT_EU_LABEL_WORK            
            break
    }
}

def deployService() {
    // Deploy the service based on the type of service (Lambda or AppRunner)
    if (env.JOB_RUN == 'Deploy AWS Lambda') {
        deployLambdaFunction(AWS_SERVICE_NAME, AWS_ECR_DESTINATION)
    } else if (env.JOB_RUN == 'Deploy App Runner Service') {
        deployAppRunnerService(AWS_SERVICE_NAME, AWS_ECR_DESTINATION)
    }
}