SHELL := /usr/bin/env bash
DOCKER_COMMAND_FLAG := $(shell which docker 2> /dev/null)
ARTIFACTORY_USER := $(ARTIFACTORY_USER)
ARTIFACTORY_PASSWORD := $(ARTIFACTORY_PASSWORD)
ARTIFACTORY_PYPY_URL := https://${ARTIFACTORY_USER}:${ARTIFACTORY_PASSWORD}@artifactory.globaldevtools.bbva.com/artifactory/api/pypi/gl-ai-genai-python-virtual/simple
PROJECT_NAME := $(shell poetry version | awk {'print $$1'})
PROJECT_VERSION := $(shell poetry version | awk {'print $$2'})
PIP_CMD := $(shell command -v pip || echo "")
PIP3_CMD := $(shell command -v pip3 || echo "")
POETRY_VERSION := 1.8.5

ifeq ($(PIP_CMD),)
	PIP_COMMAND_FLAG = pip3
else
	PIP_COMMAND_FLAG = pip
endif

ifeq ($(shell which podman 2> /dev/null),)
	ifneq ($(shell which docker 2> /dev/null),)
		DOCKER_COMMAND_FLAG := docker
	endif
else
	DOCKER_COMMAND_FLAG := podman
endif

.DEFAULT_GOAL := help

.PHONY: build
build: download-poetry configure-artifactory install  ## Build the project.

.PHONY: clean
clean:  ## Clean unused files.
	@find ./ -name '*.pyc' -o -name '__pycache__' -o -name 'Thumbs.db' -o -name '*~' -exec rm -rf {} +;
	@rm -rf .cache .pytest_cache .mypy_cache build dist *.egg-info htmlcov .tox/ docs/_build
	@echo "Project cleaned."

.PHONY: code-style
code-style:  ## Format code to comply with style guide.
	poetry run pyupgrade --py311-plus **/*.py
	poetry run isort --settings-path pyproject.toml **/*.py
	poetry run black --config pyproject.toml ./
	poetry run flake8 --config pyproject.toml ./

.PHONY: configure-artifactory
configure-artifactory:  ## Configure artifactory.
	@echo "Configuring artifactory"
	@poetry config http-basic.BBVARepo $(ARTIFACTORY_USER) $(ARTIFACTORY_PASSWORD)

.PHONY: docker-build
docker-build:  ## Build the docker image.
	@export GENAI_ENVIRONMENT=local;
	$(DOCKER_COMMAND_FLAG) build \
		--platform linux/amd64 \
		--build-arg ARTIFACTORY_USER=${ARTIFACTORY_USER} \
		--build-arg ARTIFACTORY_PASSWORD=${ARTIFACTORY_PASSWORD} \
		--build-arg GENAI_ENVIRONMENT=$(GENAI_ENVIRONMENT) \
		-t $(PROJECT_NAME):$(PROJECT_VERSION) .

.PHONY: docker-run
docker-run:  ## Run the docker image.
	-$(DOCKER_COMMAND_FLAG) network create genai-net
	$(DOCKER_COMMAND_FLAG) run --rm --platform linux/amd64 \
		-e AWS_REGION=eu-west-1 \
		-e OPENTELEMETRY_COLLECTOR_CONFIG_FILE=/var/task/collector_local.yaml \
		-p 9000:8080 \
		--network=genai-net \
		--name $(PROJECT_NAME) \
		$(PROJECT_NAME):$(PROJECT_VERSION)

.PHONY: download-cookiecutter
download-cookiecutter:  ## Download cookiecutter from Artifactory.
	@echo "Installing cookiecutter from Artifactory..."
	@$(PIP_COMMAND_FLAG) install cookiecutter --index-url $(ARTIFACTORY_PYPY_URL)
	@echo "cookiecutter installed at: $$(which cookiecutter)"

.PHONY: download-cruft
download-cruft:  ## Download cruft from Artifactory.
	@echo "Installing cruft from Artifactory..."
	@$(PIP_COMMAND_FLAG) install cruft --index-url $(ARTIFACTORY_PYPY_URL)
	@echo "cruft installed at: $$(which cruft)"

.PHONY: download-poetry
download-poetry:  ## Download poetry from Artifactory.
	@echo "Installing poetry ${POETRY_VERSION} from Artifactory..."
	@$(PIP_COMMAND_FLAG) install poetry==${POETRY_VERSION} --index-url $(ARTIFACTORY_PYPY_URL)
	@echo "Poetry installed at: $$(which poetry)"

.PHONY: help
help:  ## Show this help.
	@echo "Use: make <target>"
	@echo ""
	@echo "Targets available:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: install
install:  ## Install the project in dev mode.
	poetry install
	@echo "Don't forget to run 'make shell' if you got errors."

.PHONY: lint
lint: test check-safety check-style  ## Run all linters.

.PHONY: run-black
run-black:  ## Run black to format code.
	@echo "Running black to format code... 🪄"
	@poetry run black .

.PHONY: run-flake8
run-flake8:  ## Run flake8 to revision code format.
	@echo "Running flake8 to revision code format... 🪄"
	@poetry run flake8 --config pyproject.toml

.PHONY: run-isort
run-isort:  ## Run isort to organize imports.
	@echo "Running isort to organize imports... 🪄"
	@poetry run isort .

.PHONY: run-pautoflake
run-pautoflake:  ## Run pautoflake to delete unused imports.
	@echo "Running pautoflake to delete unused imports... 🪄"
	@poetry run pautoflake --config pyproject.toml ./src ./tests

.PHONY: shell
shell:  ## Open a virtual environment.
	poetry shell

.PHONY: test
test:  ## Run tests and generate coverage report.
	@export GENAI_ENVIRONMENT=test && \
	poetry run coverage run -m pytest --junitxml=xunit-result.xml   && \
	poetry run coverage report  && \
	poetry run coverage xml

.PHONY: run-ruff
run-ruff: run-ruff-lint run-ruff-format  ## Run ruff to delete unused imports.

.PHONY: run-ruff-lint
run-ruff-lint:  ## Run ruff lint to delete unused imports.
	@echo "Running ruff lint to delete unused imports... 🪄"
	@poetry run ruff check --fix .

.PHONY: run-ruff-format
run-ruff-format:  ## Run ruff format to delete unused imports.
	@echo "Running ruff format to delete unused imports... 🪄"
	@poetry run ruff format .
