import json
import pytest
from src.lambda_function import handler, process_event, http_response

class DummyProcessor:
    def __init__(self):
        pass
    def process(self, payload):
        return 201, {"ok": True}

@pytest.fixture(autouse=True)
def patch_processor(monkeypatch):
    import src.lambda_function as mod
    monkeypatch.setattr(mod, "processor", DummyProcessor())

def test_process_event_str_body():
    evt = {"body": json.dumps({"a": 1})}
    assert process_event(evt) == {"a": 1}

def test_process_event_dict_body():
    evt = {"body": {"a": 2}}
    assert process_event(evt) == {"a": 2}

def test_http_response():
    r = http_response(202, {"x": 3})
    assert r["statusCode"] == 202
    assert json.loads(r["body"]) == {"x": 3}

def test_handler(monkeypatch):
    evt = {"body": json.dumps({"b": 4})}
    out = handler(evt, None)
    assert out["statusCode"] == 201
    assert json.loads(out["body"]) == {"ok": True}
