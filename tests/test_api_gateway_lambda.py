import json
import pytest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the src directory to the path to import the lambda function
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.lambda_function import handler, process_event


class TestAPIGatewayLambda:

    def test_process_event_query_params(self):
        """Test parsing filename from query parameters"""
        event = {
            'queryStringParameters': {
                'filename': 'test_file.7z',
                'bucket': 'test-bucket'
            }
        }

        result = process_event(event)
        assert result['filename'] == 'test_file.7z'
        assert result['bucket'] == 'test-bucket'

    def test_process_event_body(self):
        """Test parsing filename from request body"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': json.dumps({
                'filename': 'body_file.7z',
                'bucket': 'body-bucket'
            })
        }

        result = process_event(event)
        assert result['filename'] == 'body_file.7z'
        assert result['bucket'] == 'body-bucket'

    def test_process_event_no_filename(self):
        """Test parsing when no filename is provided"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }

        result = process_event(event)
        assert result['filename'] is None
        assert result['bucket'] is None
    
    @patch('src.application.processor.TasacionProcessor')
    def test_handler_success(self, mock_processor_class):
        """Test successful handler execution"""
        mock_processor = Mock()
        mock_processor.process.return_value = (200, {"message": "Success"})
        mock_processor_class.return_value = mock_processor

        event = {
            'queryStringParameters': {
                'filename': 'test.7z'
            }
        }

        response = handler(event, None)

        assert response['statusCode'] == 200
        body = json.loads(response['body'])
        assert body['message'] == 'Success'

    def test_handler_missing_filename(self):
        """Test handler with missing filename"""
        event = {
            'queryStringParameters': None,
            'pathParameters': None,
            'body': None
        }

        response = handler(event, None)

        assert response['statusCode'] == 400
        body = json.loads(response['body'])
        assert 'filename' in body['error']

    @patch('src.application.processor.TasacionProcessor')
    def test_handler_file_extension_added(self, mock_processor_class):
        """Test that .7z extension is added automatically"""
        mock_processor = Mock()
        mock_processor.process.return_value = (200, {"message": "Success"})
        mock_processor_class.return_value = mock_processor

        event = {
            'queryStringParameters': {
                'filename': 'test_file'  # No extension
            }
        }

        response = handler(event, None)

        # Check that the processor was called with .7z extension added
        call_args = mock_processor.process.call_args[0][0]
        assert call_args['files'][0] == 'test_file.7z'

    @patch('src.application.processor.TasacionProcessor')
    def test_handler_custom_bucket(self, mock_processor_class):
        """Test handler with custom bucket"""
        mock_processor = Mock()
        mock_processor.process.return_value = (200, {"message": "Success"})
        mock_processor_class.return_value = mock_processor

        event = {
            'body': json.dumps({
                'filename': 'test.7z',
                'bucket': 'custom-bucket'
            })
        }

        response = handler(event, None)

        # Check that the processor was called with custom bucket
        call_args = mock_processor.process.call_args[0][0]
        assert call_args['bucket'] == 'custom-bucket'


if __name__ == '__main__':
    pytest.main([__file__])
