import io
import zipfile
import pytest
from src.domain.models import TasacionRequest
from src.domain.services import TasacionValidator, FileUnzipper

class DummyS3:
    def __init__(self, existing_keys):
        self.existing = set(existing_keys)
        self.storage = {}
    def exists(self, bucket, key):
        return key in self.existing
    def get_object(self, bucket, key):
        return self.storage[key]
    def put_object(self, bucket, key, content):
        self.storage[key] = content

def make_zip_bytes(contents: dict[str, bytes]) -> bytes:
    buf = io.BytesIO()
    with zipfile.ZipFile(buf, 'w') as zf:
        for name, data in contents.items():
            zf.writestr(name, data)
    return buf.getvalue()

def test_validator_all_exist():
    s3 = DummyS3(["a.txt", "b.txt"])
    req = TasacionRequest("1", ["a.txt", "b.txt"])
    ok, found = TasacionValidator.validate_all_exist(req, "bucket", s3)
    assert ok is True
    assert set(found) == {"a.txt", "b.txt"}

def test_validator_some_missing():
    s3 = DummyS3(["a.txt"])
    req = TasacionRequest("1", ["a.txt", "c.txt"])
    ok, found = TasacionValidator.validate_all_exist(req, "bucket", s3)
    assert ok is False
    assert found == ["a.txt"]

def test_file_unzipper(monkeypatch):
    # Prepara un zip con dos archivos
    zipped = make_zip_bytes({"f1.txt": b"foo", "f2.txt": b"bar"})
    s3 = DummyS3(["archive.zip"])
    s3.storage["archive.zip"] = zipped

    FileUnzipper.unzip_and_upload("archive.zip", "bucket", s3)

    # Debe haber reenviado ambos ficheros descomprimidos
    expected_keys = {
        "unzipped/archive/f1.txt",
        "unzipped/archive/f2.txt",
    }
    assert set(s3.storage.keys()).intersection(expected_keys) == expected_keys
    assert s3.storage["unzipped/archive/f1.txt"] == b"foo"
    assert s3.storage["unzipped/archive/f2.txt"] == b"bar"