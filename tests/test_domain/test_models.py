import pytest
from src.domain.models import TasacionRequest

def test_tasacion_request_valid():
    req = TasacionRequest("TAS-001", ["doc1.pdf", "doc2.zip"])
    assert req.id_tasacion == "TAS-001"
    assert req.files == ["doc1.pdf", "doc2.zip"]

def test_tasacion_request_empty_id():
    with pytest.raises(ValueError):
        TasacionRequest("", ["file.txt"])

def test_tasacion_request_empty_files():
    with pytest.raises(ValueError):
        TasacionRequest("TAS-002", [])