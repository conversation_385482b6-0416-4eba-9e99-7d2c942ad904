import pytest

from src.domain.genai.llm import AbstractLlmUnitOfWork


def test_cannot_instantiate_abstract_class():
    """Ensure instantiating AbstractLlmUnitOfWork raises TypeError."""
    with pytest.raises(TypeError):
        AbstractLlmUnitOfWork()


def test_enter_exit_methods():
    """Ensure a concrete implementation can be used as a context manager."""

    class MockLLMUnitOfWork(AbstractLlmUnitOfWork):
        def execute_prompt(
            self, prompt, model=None, temperature=0.5, max_tokens=4000
        ):
            pass

        def get(self):
            pass

    with MockLLMUnitOfWork() as uow:
        assert uow is not None


def test_get_method_not_implemented():
    """Ensure a subclass without get method raises TypeError."""

    class IncompleteLLMUnitOfWork(AbstractLlmUnitOfWork):
        pass

    with pytest.raises(TypeError):
        IncompleteLLMUnitOfWork()


def test_get_method_is_called():
    """Ensure concrete implementation returns expected result."""

    class ConcreteLLMUnitOfWork(AbstractLlmUnitOfWork):
        def execute_prompt(
            self, prompt, model=None, temperature=0.5, max_tokens=4000
        ):
            pass

        def get(self):
            self._model = "test"
            return self._model

    uow = ConcreteLLMUnitOfWork()
    assert uow.get() == "test"
