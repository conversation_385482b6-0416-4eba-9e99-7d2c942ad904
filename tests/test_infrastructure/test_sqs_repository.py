import json
from unittest.mock import Magic<PERSON>ock
import pytest
from src.infrastructure.sqs_repository import SQSRepository

@pytest.fixture(autouse=True)
def mock_client(monkeypatch):
    client = MagicMock()
    monkeypatch.setattr("src.infrastructure.sqs_repository.boto3.client", lambda *args, **kw: client)
    return client

def test_send_message(mock_client):
    repo = SQSRepository(queue_url="https://sqs.test/queue")
    repo.send_message({"x": 1})
    mock_client.send_message.assert_called_once_with(
        QueueUrl="https://sqs.test/queue",
        MessageBody=json.dumps({"x": 1})
    )
