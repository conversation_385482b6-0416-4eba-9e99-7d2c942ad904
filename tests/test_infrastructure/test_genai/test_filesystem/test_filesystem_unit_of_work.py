import io
import unittest
from unittest.mock import patch

import src.infrastructure.genai.filesystem as genai_filesystem


class TestS3FileSystemUnitOfWork(unittest.TestCase):

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_s3_file_system_unit_of_work(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_boto3.client.assert_called_with("s3")
        self.assertEqual(uow._route, genai_filesystem.genai_config.S3_BUCKET)

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_get_file(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful file retrieval
        mock_s3_client.get_object.return_value = {
            "Body": io.BytesIO(b"test content")
        }
        file_content = uow.get_file("test_file.txt")
        self.assertEqual(file_content, b"test content")
        mock_s3_client.get_object.assert_called_once_with(
            Bucket=genai_filesystem.genai_config.S3_BUCKET,
            Key="test_file.txt",
        )

        # Test file not found
        mock_s3_client.get_object.side_effect = Exception("NoSuchKey")
        with self.assertRaises(Exception):
            uow.get_file("non_existent_file.txt")

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_upload_file(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful file upload
        self.assertTrue(uow.upload_file("local/file.txt", "s3_file.txt"))
        mock_s3_client.upload_file.assert_called_once_with(
            "local/file.txt",
            genai_filesystem.genai_config.S3_BUCKET,
            "s3_file.txt",
        )

        # Test file upload failure
        mock_s3_client.upload_file.side_effect = Exception(
            "Error uploading file"
        )
        self.assertFalse(uow.upload_file("local/file.txt", "s3_file.txt"))

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_create_text_file(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful text file creation
        self.assertTrue(uow.create_text_file("test content", "test_file.txt"))
        mock_s3_client.upload_fileobj.assert_called_once()
        args, _ = mock_s3_client.upload_fileobj.call_args
        self.assertEqual(args[0].read(), b"test content")
        self.assertEqual(args[1], genai_filesystem.genai_config.S3_BUCKET)
        self.assertEqual(args[2], "test_file.txt")

        # Test text file creation failure
        mock_s3_client.upload_fileobj.side_effect = Exception(
            "Error uploading file"
        )
        self.assertFalse(uow.create_text_file("test content", "test_file.txt"))

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_list_folder(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful folder listing
        mock_s3_client.list_objects_v2.return_value = {
            "Contents": [
                {"Key": "folder/file1.txt"},
                {"Key": "folder/file2.txt"},
                {"Key": "folder/"},
            ]
        }
        objects = uow.list_folder("folder/")
        self.assertEqual(objects, ["folder/file1.txt", "folder/file2.txt"])

        # Test folder listing failure
        mock_s3_client.list_objects_v2.side_effect = Exception(
            "Error listing objects"
        )
        with self.assertRaises(Exception):
            uow.list_folder("folder/")

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_move_file(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful file move
        self.assertTrue(uow.move_file("old_file.txt", "new_file.txt"))
        mock_s3_client.copy_object.assert_called_once_with(
            Bucket=genai_filesystem.genai_config.S3_BUCKET,
            CopySource={
                "Bucket": genai_filesystem.genai_config.S3_BUCKET,
                "Key": "old_file.txt",
            },
            Key="new_file.txt",
        )
        mock_s3_client.delete_object.assert_called_once_with(
            Bucket=genai_filesystem.genai_config.S3_BUCKET,
            Key="old_file.txt",
        )

        # Test file move failure
        mock_s3_client.copy_object.side_effect = Exception(
            "Error copying file"
        )
        self.assertFalse(uow.move_file("old_file.txt", "new_file.txt"))

    @patch("src.infrastructure.genai.filesystem.boto3")
    def test_delete_file(self, mock_boto3):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        mock_s3_client = mock_boto3.client.return_value

        # Test successful file deletion
        self.assertTrue(uow.delete_file("test_file.txt"))
        mock_s3_client.delete_object.assert_called_once_with(
            Bucket=genai_filesystem.genai_config.S3_BUCKET,
            Key="test_file.txt",
        )

        # Test file deletion failure
        mock_s3_client.delete_object.side_effect = Exception(
            "Error deleting file"
        )
        self.assertFalse(uow.delete_file("test_file.txt"))

    def test_move_route(self):
        uow = genai_filesystem.S3FileSystemUnitOfWork()
        uow.move_route("new_bucket")
        self.assertEqual(uow._route, "new_bucket")
