import botocore
import pytest
from unittest.mock import MagicMock
from src.infrastructure.s3_repository import S3Repository

@pytest.fixture(autouse=True)
def mock_client(monkeypatch):
    client = MagicMock()
    monkeypatch.setattr("src.infrastructure.s3_repository.boto3.client", lambda *args, **kw: client)
    return client

def test_exists_true(mock_client):
    mock_client.head_object.return_value = {}
    repo = S3Repository()
    assert repo.exists("bucket", "key") is True
    mock_client.head_object.assert_called_once_with(Bucket="bucket", Key="key")

def test_exists_false(mock_client):
    error = botocore.exceptions.ClientError({"Error": {}}, "HeadObject")
    mock_client.head_object.side_effect = error
    repo = S3Repository()
    assert repo.exists("bucket", "key") is False

def test_get_object(mock_client):
    body = MagicMock(read=MagicMock(return_value=b"data"))
    mock_client.get_object.return_value = {"Body": body}
    repo = S3Repository()
    result = repo.get_object("bucket", "key")
    assert result == b"data"
    mock_client.get_object.assert_called_once_with(Bucket="bucket", Key="key")

def test_put_object(mock_client):
    repo = S3Repository()
    repo.put_object("bucket", "key", b"xyz")
    mock_client.put_object.assert_called_once_with(Bucket="bucket", Key="key", Body=b"xyz")
